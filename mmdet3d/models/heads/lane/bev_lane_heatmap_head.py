import torch
import torch.nn as nn
import torch.nn.functional as F
from mmcv.cnn import ConvModule
from mmcv.runner import BaseModule, force_fp32
import numpy as np
from sklearn.cluster import DBSCAN
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import os

from mmdet3d.models.builder import HEADS, build_loss
from mmdet.models.builder import LOSSES


@LOSSES.register_module()
class DiscriminativeLoss(nn.Module):
    """Push-Pull loss for instance embeddings.
    
    This loss encourages embeddings from the same instance to be close together
    (pull) and embeddings from different instances to be far apart (push).
    
    Args:
        delta_v (float): Margin for the pull loss.
        delta_d (float): Margin for the push loss.
        norm (int): Norm to use for the distance calculation.
        alpha (float): Weight for the pull loss. 
        beta (float): Weight for the push loss.
        gamma (float): Weight for the regularization loss.
    """
    def __init__(self, delta_v=0.5, delta_d=1.5, norm=1, alpha=1.0, beta=1.0, gamma=0.001, loss_weight=1.0, **kwargs):
        super(DiscriminativeLoss, self).__init__()
        self.delta_v = delta_v
        self.delta_d = delta_d
        self.norm = norm
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.weight = loss_weight
    
    @force_fp32()
    def forward(self, embeddings, instance_ids, valid_mask=None):
        """Vectorized forward pass for the discriminative loss."""
        batch_size, embed_dim, height, width = embeddings.size()
        
        # =============================================================================
        # CRITICAL DTYPE FIX: Ensure proper tensor dtypes for bitwise operations
        # =============================================================================
        # print(f"[EMBEDDING_LOSS_DEBUG] === DISCRIMINATIVE LOSS FORWARD ===")
        # print(f"[EMBEDDING_LOSS_DEBUG] Input tensor dtypes:")
        # print(f"[EMBEDDING_LOSS_DEBUG]   embeddings: {embeddings.dtype}, shape: {embeddings.shape}")
        # print(f"[EMBEDDING_LOSS_DEBUG]   instance_ids: {instance_ids.dtype}, shape: {instance_ids.shape}")
        # if valid_mask is not None:
        #     print(f"[EMBEDDING_LOSS_DEBUG]   valid_mask: {valid_mask.dtype}, shape: {valid_mask.shape}")
        #     print(f"[EMBEDDING_LOSS_DEBUG]   valid_mask range: [{valid_mask.min():.3f}, {valid_mask.max():.3f}]")
        
        # Reshape tensors for batch processing
        embeddings = embeddings.permute(0, 2, 3, 1).reshape(batch_size, -1, embed_dim)
        instance_ids = instance_ids.reshape(batch_size, -1)
        
        if valid_mask is not None:
            valid_mask = valid_mask.reshape(batch_size, -1)
            # CRITICAL FIX: Convert float mask to boolean for bitwise operations
            if valid_mask.dtype != torch.bool:
                # print(f"[EMBEDDING_LOSS_DEBUG] Converting valid_mask from {valid_mask.dtype} to bool")
                valid_mask = valid_mask > 0.5  # Convert to boolean
                # print(f"[EMBEDDING_LOSS_DEBUG] After conversion: {valid_mask.dtype}")
        else:
            valid_mask = torch.ones_like(instance_ids, dtype=torch.bool)
            # print(f"[EMBEDDING_LOSS_DEBUG] Created default valid_mask with dtype: {valid_mask.dtype}")
        
        # Debug instance ID statistics
        unique_instances = torch.unique(instance_ids)
        # print(f"[EMBEDDING_LOSS_DEBUG] Instance ID statistics:")
        # print(f"[EMBEDDING_LOSS_DEBUG]   Unique instance IDs: {unique_instances.tolist()}")
        # print(f"[EMBEDDING_LOSS_DEBUG]   Valid mask positive count: {valid_mask.sum().item()}")
        
        # Check foreground instance availability
        foreground_mask = valid_mask & (instance_ids > 0)
        # print(f"[EMBEDDING_LOSS_DEBUG]   Foreground instances count: {foreground_mask.sum().item()}")
        
        if foreground_mask.sum() == 0:
            # print(f"[EMBEDDING_LOSS_WARN] No foreground instances found - returning zero loss")
            zero_loss = torch.tensor(0., device=embeddings.device, requires_grad=True)
            return {
                'loss': zero_loss,
                'pull_loss': zero_loss,
                'push_loss': zero_loss,
                'reg_loss': zero_loss
            }
            
        pull_loss = torch.tensor(0., device=embeddings.device, requires_grad=True)
        push_loss = torch.tensor(0., device=embeddings.device, requires_grad=True)
        reg_loss = torch.tensor(0., device=embeddings.device, requires_grad=True)
        
        valid_batches = 0
        
        for b in range(batch_size):
            # Select valid foreground points for the current sample (instance_ids > 0)
            # FIXED: Now both tensors are boolean, so bitwise AND will work
            sample_mask = valid_mask[b] & (instance_ids[b] > 0)
            
            # print(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: sample_mask sum = {sample_mask.sum().item()}")
            
            if not sample_mask.any():
                # print(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: No valid samples, skipping")
                continue
                
            sample_embeddings = embeddings[b][sample_mask]
            sample_instances = instance_ids[b][sample_mask]
            
            # Get unique instances and their counts
            unique_instances, unique_inverse, unique_counts = torch.unique(
                sample_instances, return_inverse=True, return_counts=True
            )
            num_instances = unique_instances.size(0)
            
            # print(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: {num_instances} unique instances")
            # print(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: Instance counts: {unique_counts.tolist()}")

            if num_instances <= 1:
                # print(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: Not enough instances for discriminative loss")
                continue

            valid_batches += 1

            # --- Calculate instance centroids (means) using scatter_add for efficiency ---
            # DTYPE_FIX: Ensure sum_embeddings has the same dtype as sample_embeddings
            sum_embeddings = torch.zeros(num_instances, embed_dim, device=embeddings.device, dtype=sample_embeddings.dtype)
            # print(f"[DTYPE_DEBUG] sum_embeddings dtype: {sum_embeddings.dtype}, sample_embeddings dtype: {sample_embeddings.dtype}")
            sum_embeddings.scatter_add_(0, unique_inverse.unsqueeze(1).expand_as(sample_embeddings), sample_embeddings)
            centroids = sum_embeddings / (unique_counts.unsqueeze(1).float() + 1e-8)

            # --- Calculate Pull Loss (intra-instance variance) ---
            dists_to_centroids = torch.norm(sample_embeddings - centroids[unique_inverse], p=self.norm, dim=1)
            pull_per_point = torch.clamp(dists_to_centroids - self.delta_v, min=0)**2
            # DTYPE_FIX: Ensure pull_per_instance has the same dtype as pull_per_point
            pull_per_instance = torch.zeros(num_instances, device=embeddings.device, dtype=pull_per_point.dtype)
            pull_per_instance.scatter_add_(0, unique_inverse, pull_per_point)
            # Average variance per instance, then average over all instances
            batch_pull_loss = torch.mean(pull_per_instance / (unique_counts.float() + 1e-8))
            pull_loss = pull_loss + batch_pull_loss

            # --- Calculate Push Loss (inter-instance distance) ---
            # Pairwise distance between centroids
            centroid_dists = torch.cdist(centroids, centroids, p=self.norm)
            # Penalize centroids that are too close, ensuring we only consider each pair once (upper triangle)
            push = torch.clamp(2 * self.delta_d - centroid_dists, min=0)**2
            batch_push_loss = torch.sum(torch.triu(push, diagonal=1)) / (num_instances * (num_instances - 1) / 2 + 1e-8)
            push_loss = push_loss + batch_push_loss
            
            # --- Calculate Regularization Loss ---
            batch_reg_loss = torch.mean(torch.norm(centroids, p=self.norm, dim=1))
            reg_loss = reg_loss + batch_reg_loss
            
            # print(f"[EMBEDDING_LOSS_DEBUG] Batch {b} losses: pull={batch_pull_loss.item():.6f}, push={batch_push_loss.item():.6f}, reg={batch_reg_loss.item():.6f}")

        # Average losses over valid batches
        if valid_batches > 0:
            pull_loss = pull_loss / valid_batches
            push_loss = push_loss / valid_batches
            reg_loss = reg_loss / valid_batches
            
            # print(f"[EMBEDDING_LOSS_DEBUG] Final averaged losses over {valid_batches} batches:")
            # print(f"[EMBEDDING_LOSS_DEBUG]   pull_loss: {pull_loss.item():.6f}")
            # print(f"[EMBEDDING_LOSS_DEBUG]   push_loss: {push_loss.item():.6f}")
            # print(f"[EMBEDDING_LOSS_DEBUG]   reg_loss: {reg_loss.item():.6f}")

        # Combine losses with weights
        total_loss = (self.alpha * pull_loss + self.beta * push_loss + self.gamma * reg_loss) * self.weight

        # print(f"[EMBEDDING_LOSS_DEBUG] Total loss: {total_loss.item():.6f}")
        # print(f"[EMBEDDING_LOSS_DEBUG] =======================================")

        return {
            'loss': total_loss,
            'pull_loss': self.alpha * pull_loss,
            'push_loss': self.beta * push_loss,
            'reg_loss': self.gamma * reg_loss
        }


@HEADS.register_module()
class BEVLaneHeatmapHead(BaseModule):
    """Anchor-free BEV lane detection head using heatmap-based approach.
    
    This head predicts lane lines as a set of keypoints at predefined longitudinal (y) positions.
    For each y-position, it predicts:
    1. A heatmap indicating lane presence probability
    2. Local y-offsets for precise localization
    3. Z-height values for 3D reconstruction
    4. Lane classification logits for lane type
    5. Instance embeddings for lane grouping (new!)
    
    This representation is efficient and easily interpretable, making it suitable for
    real-time inference on edge devices like NVIDIA Orin.
    """

    def __init__(self,
                 in_channels=256,
                 feat_channels=64,
                 num_classes=13,
                 grid_conf=None,
                 row_points=100,
                 z_range=[-1.0, 3.0],
                 max_lanes=20,
                 hm_thres=0.5,
                 nms_kernel_size=5,
                 use_sigmoid=True,
                 loss_heatmap=None,
                 loss_offset=None,
                 loss_z=None,
                 loss_cls=None,
                 loss_embedding=None,
                 use_embedding=False,
                 embedding_dim=8,
                 group_lanes=True,
                 lane_group_min_distance=1.0,
                 clustering_method='dbscan',
                 clustering_epsilon=0.5,
                 clustering_min_points=3,
                 debug_clustering=False,
                 init_cfg=None,
                 **kwargs):
        """Initialize BEVLaneHeatmapHead.
        
        Args:
            in_channels (int): Number of input channels.
            feat_channels (int): Number of feature channels in conv layers.
            num_classes (int): Number of lane classes.
            grid_conf (dict): Configuration of BEV grid, including dimensions and ranges.
            row_points (int): Number of points to sample along y-axis (longitudinal).
            z_range (list): Range of z-height prediction [min, max].
            max_lanes (int): Maximum number of lane instances to detect.
            hm_thres (float): Heatmap threshold for detecting lane points.
            nms_kernel_size (int): Kernel size for NMS on heatmap.
            use_sigmoid (bool): Whether to use sigmoid for heatmap.
            loss_heatmap (dict): Config of heatmap loss.
            loss_offset (dict): Config of offset loss.
            loss_z (dict): Config of z-height loss.
            loss_cls (dict): Config of classification loss.
            loss_embedding (dict): Config of embedding loss (new!).
            use_embedding (bool): Whether to use learned embeddings for lane grouping (new!).
            embedding_dim (int): Dimension of embedding vectors (new!).
            group_lanes (bool): Whether to group lane points into lane instances.
            lane_group_min_distance (float): Minimum distance (in meters) to group lane points.
            clustering_method (str): Method for clustering embeddings ('dbscan' or 'geometric') (new!).
            clustering_epsilon (float): Epsilon parameter for DBSCAN clustering (new!).
            clustering_min_points (int): Minimum points parameter for DBSCAN clustering (new!).
            debug_clustering (bool): Whether to enable debug visualization for clustering (new!).
            init_cfg (dict): Initialization config.
        """
        super(BEVLaneHeatmapHead, self).__init__(init_cfg=init_cfg)
        self.in_channels = in_channels
        self.feat_channels = feat_channels
        self.num_classes = num_classes
        self.row_points = row_points
        self.z_range = z_range
        self.max_lanes = max_lanes
        self.hm_thres = hm_thres
        self.nms_kernel_size = nms_kernel_size
        self.use_sigmoid = use_sigmoid
        self.group_lanes = group_lanes
        self.lane_group_min_distance = lane_group_min_distance
        
        # New parameters for instance embedding
        self.use_embedding = use_embedding
        self.embedding_dim = embedding_dim
        self.clustering_method = clustering_method
        self.clustering_epsilon = clustering_epsilon
        self.clustering_min_points = clustering_min_points
        self.debug_clustering = debug_clustering
        
        # Validate grid configuration
        if grid_conf is None:
            raise ValueError("`grid_conf` must be provided to define BEV grid dimensions.")
        required_keys = ['xbound', 'ybound']
        for key in required_keys:
            if key not in grid_conf:
                raise ValueError(f"`grid_conf` is missing required key: `{key}`")
        
        self.grid_conf = grid_conf
        
        # Extract grid configuration
        self.x_min = grid_conf['xbound'][0]
        self.x_max = grid_conf['xbound'][1]
        self.x_res = grid_conf['xbound'][2]
        self.y_min = grid_conf['ybound'][0]
        self.y_max = grid_conf['ybound'][1]
        self.y_res = grid_conf['ybound'][2]
        
        # Number of grid cells
        self.nx = int((self.x_max - self.x_min) / self.x_res)
        self.ny = int((self.y_max - self.y_min) / self.y_res)
        
        # Build network
        self._build_layers()
        
        # Define losses
        from mmdet.models.losses import GaussianFocalLoss, L1Loss, CrossEntropyLoss
        
        self.loss_heatmap = GaussianFocalLoss(reduction='mean') if loss_heatmap is None else \
            build_loss(loss_heatmap)
        self.loss_offset = L1Loss(reduction='mean') if loss_offset is None else \
            build_loss(loss_offset)
        self.loss_z = L1Loss(reduction='mean') if loss_z is None else \
            build_loss(loss_z)
        self.loss_cls = CrossEntropyLoss(use_sigmoid=False, reduction='mean', ignore_index=0) if loss_cls is None else \
            build_loss(loss_cls)
        
        # Initialize embedding loss if using embeddings
        if self.use_embedding:
            if loss_embedding is None:
                self.loss_embedding = DiscriminativeLoss()
            else:
                # Check if 'type' is in loss_embedding
                if isinstance(loss_embedding, dict) and 'type' not in loss_embedding:
                    # If no type is provided, directly use DiscriminativeLoss with provided params
                    delta_v = loss_embedding.get('delta_v', 0.5)
                    delta_d = loss_embedding.get('delta_d', 1.5)
                    norm = loss_embedding.get('norm', 1)
                    alpha = loss_embedding.get('alpha', 1.0)
                    beta = loss_embedding.get('beta', 1.0)
                    gamma = loss_embedding.get('gamma', 0.001)
                    weight = loss_embedding.get('loss_weight', 1.0)
                    self.loss_embedding = DiscriminativeLoss(
                        delta_v=delta_v,
                        delta_d=delta_d,
                        norm=norm,
                        alpha=alpha,
                        beta=beta,
                        gamma=gamma,
                        weight=weight
                    )
                else:
                    self.loss_embedding = build_loss(loss_embedding)
        
        # CORRECT: Pre-compute x-positions for sampling (along forward direction)
        # 车道线沿x轴(前进方向)延伸，应该在固定x位置进行采样
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        x_vals = torch.linspace(self.x_min, self.x_max, self.row_points, device=device)
        self.register_buffer('x_positions', x_vals)
        print(f"[BEV_HEAD_INIT] Registered x_positions buffer: {self.x_min} to {self.x_max}, {self.row_points} points")
    
    def _build_layers(self):
        """Build network layers."""
        # Shared feature extraction layers
        self.feat_conv = nn.Sequential(
            ConvModule(
                self.in_channels,
                int(self.in_channels/2),
                kernel_size=3,
                padding=1,
                conv_cfg=dict(type='Conv2d'),
                norm_cfg=dict(type='BN2d'),
                act_cfg=dict(type='ReLU')),
            ConvModule(
                int(self.in_channels/2),
                self.feat_channels,
                kernel_size=3,
                padding=1,
                conv_cfg=dict(type='Conv2d'),
                norm_cfg=dict(type='BN2d'),
                act_cfg=dict(type='ReLU'))
        )
        
        # Task-specific heads
        # Heatmap for lane presence at each location
        self.heatmap_head = nn.Conv2d(
            self.feat_channels, 1, kernel_size=3, padding=1)
        
        # Y-offset prediction (local offset from grid center)
        self.offset_head = nn.Conv2d(
            self.feat_channels, 1, kernel_size=3, padding=1)
        
        # Z-height prediction for 3D coordinates
        self.z_head = nn.Conv2d(
            self.feat_channels, 1, kernel_size=3, padding=1)
        
        # Classification head for lane types
        self.cls_head = nn.Conv2d(
            self.feat_channels, self.num_classes, kernel_size=3, padding=1)
        
        # Instance embedding head for lane grouping (new!)
        if self.use_embedding:
            self.embedding_head = nn.Conv2d(
                self.feat_channels, self.embedding_dim, kernel_size=3, padding=1)
    
    def forward(self, x, depth_features=None):
        """Forward function.
        
        Args:
            x (torch.Tensor): Input BEV feature map of shape (B, C, H, W).
            depth_features (torch.Tensor, optional): Additional depth features.
        
        Returns:
            tuple: A tuple containing output predictions:
                - heatmap: Lane presence heatmap.
                - offset: Y-coordinate offset predictions.
                - z_pred: Z-height predictions.
                - cls_pred: Lane classification logits.
                - embed_pred: Instance embedding predictions (if use_embedding=True).
        """
        # Shared feature extraction
        feat = self.feat_conv(x)
        
        # Task-specific prediction
        heatmap = self.heatmap_head(feat)
        if self.use_sigmoid:
            heatmap = torch.sigmoid(heatmap)
        
        # Apply tanh to constrain offset predictions to [-0.5, 0.5] grid cells
        offset = 0.5 * torch.tanh(self.offset_head(feat))
        
        z_pred = self.z_head(feat)
        # Scale z prediction to proper range
        z_pred = (self.z_range[1] - self.z_range[0]) * torch.sigmoid(z_pred) + self.z_range[0]
        
        cls_pred = self.cls_head(feat)
        
        # If using embeddings, predict them
        if self.use_embedding:
            embed_pred = self.embedding_head(feat)
            return heatmap, offset, z_pred, cls_pred, embed_pred
        else:
            return heatmap, offset, z_pred, cls_pred
    
    def _transform_lane_targets(self, lane_targets):
        """Transform lane targets dictionary from data pipeline to expected formats.
        
        Args:
            lane_targets (dict): Lane targets from data pipeline.
            
        Returns:
            dict: Transformed lane targets.
        """
        # print(f"[LANE_HEAD_DEBUG] lane_targets keys available: {list(lane_targets.keys() if lane_targets else [])}")
        
        if lane_targets is None or not lane_targets:
            # print("[LANE_HEAD_DEBUG] lane_targets is None or empty")
            return None
            
        device = None
        for k, v in lane_targets.items():
            if isinstance(v, list) and len(v) > 0 and isinstance(v[0], torch.Tensor):
                device = v[0].device
                break
        
        if device is None:
            # print("[LANE_HEAD_DEBUG] Could not determine device from lane_targets")
            return None

        transformed_targets = {}
        
        # CRITICAL FIX: Handle both old 'segmentation' and new 'gt_heatmap' naming
        # Avoid boolean conversion issues with tensors by checking for None explicitly
        heatmap_source = None
        if 'gt_heatmap' in lane_targets and lane_targets['gt_heatmap'] is not None:
            heatmap_source = lane_targets['gt_heatmap']
            # print("[LANE_HEAD_DEBUG] Found gt_heatmap in lane_targets")
        elif 'segmentation' in lane_targets and lane_targets['segmentation'] is not None:
            heatmap_source = lane_targets['segmentation']
            # print("[LANE_HEAD_DEBUG] Found segmentation in lane_targets") 
        else:
            # print("[LANE_HEAD_DEBUG] No gt_heatmap or segmentation in lane_targets")
            # print(f"[LANE_HEAD_DEBUG] Available keys: {list(lane_targets.keys())}")
            return None
            
        # 统一的数据提取函数
        def _extract_tensor_from_dc(value):
            """统一处理 DataContainer 和其他数据类型的提取"""
            from mmcv.parallel import DataContainer as DC
            
            if isinstance(value, DC):
                actual_data = value.data
                if isinstance(actual_data, (list, tuple)) and len(actual_data) > 0:
                    return actual_data[0]
                return actual_data
            elif isinstance(value, (list, tuple)) and len(value) > 0:
                item = value[0]
                if isinstance(item, DC):
                    return item.data
                return item
            return value
        
        # 处理热图源数据
        transformed_targets['gt_heatmap'] = _extract_tensor_from_dc(heatmap_source)
            
        # 优化的键映射处理，使用统一的数据提取函数
        key_mappings = [
            ('gt_offset', 'gt_offset'),      # 直接映射
            ('embedding', 'gt_offset'),      # 兼容旧命名
            ('gt_z', 'gt_height'),           # 管道输出 gt_z，头部期望 gt_height
            ('height_map', 'gt_height'),     # 兼容旧命名
            ('gt_mask', 'gt_mask'),          # 直接映射
            ('mask', 'gt_mask'),             # 兼容旧命名
            ('gt_cls', 'gt_cls'),            # 直接映射
            ('gt_instance_ids', 'gt_instance_ids')  # 直接映射
        ]
        
        for src_key, dst_key in key_mappings:
            if src_key in lane_targets and lane_targets[src_key] is not None:
                value = lane_targets[src_key]
                
                # 使用统一的数据提取函数
                extracted_value = _extract_tensor_from_dc(value)
                
                # 验证提取的数据
                if isinstance(extracted_value, torch.Tensor) and extracted_value.numel() == 0:
                    continue  # 跳过空张量
                
                transformed_targets[dst_key] = extracted_value
                
                # tensor = transformed_targets[dst_key]
                # print(f"[LANE_HEAD_DEBUG] {src_key} shape: {tensor.shape}")
                
                # if src_key == 'gt_cls' and tensor.shape[0] > 1:
                #     print(f"[LANE_HEAD_DEBUG] Class distribution:")
                #     for i in range(tensor.shape[0]):
                #         class_sum = tensor[i].sum().item()
                #         if class_sum > 0:
                #             print(f"[LANE_HEAD_DEBUG]   - Class {i}: {class_sum} points")
                
                # if src_key == 'mask':
                #     mask_sum = tensor.sum().item()
                #     print(f"[LANE_HEAD_DEBUG] Mask has {mask_sum} positive points")
                
                # if src_key == 'gt_instance_ids':
                #     unique_ids = torch.unique(tensor)
                #     print(f"[LANE_HEAD_DEBUG] Instance IDs: {unique_ids.tolist()}")
                #     for id_value in unique_ids:
                #         if id_value == 0: 
                #             continue
                #         points_count = (tensor == id_value).sum().item()
                #         print(f"[LANE_HEAD_DEBUG]   - Instance {id_value}: {points_count} points")
        
        # if 'gt_heatmap' in transformed_targets and torch.max(transformed_targets['gt_heatmap']) > 0:
        #     heatmap = transformed_targets['gt_heatmap']
            # print(f"[LANE_HEAD_DEBUG] Valid heatmap found: min={torch.min(heatmap)}, max={torch.max(heatmap)}")
            # print(f"[LANE_HEAD_DEBUG] Number of values > 0.5: {(heatmap > 0.5).sum().item()}")
            # print(f"[LANE_HEAD_DEBUG] Number of values > 0.9: {(heatmap > 0.9).sum().item()}")
        # else:
            # print("[LANE_HEAD_DEBUG] No valid values in heatmap") # Keep this important warning
        
        # # 在返回之前添加调试
        # if transformed_targets:
        #     print(f"[TRANSFORM_DEBUG] Transformed target shapes:")
        #     for key, value in transformed_targets.items():
        #         print(f"[TRANSFORM_DEBUG] {key}: {value.shape}")
        
        return transformed_targets
        
    def loss(self, preds, lane_targets, **kwargs):
        """Compute losses following the BEVFusion framework pattern.
        
        Args:
            preds (tuple): Tuple of predictions (heatmap, offset, z_pred, cls_pred, [embed_pred]).
            lane_targets (dict): Ground truth targets from the dataset pipeline.
                                 Expected keys from GenerateBEVLaneHeatmapTargets:
                                 'segmentation', 'embedding', 'height_map', 'mask', 'gt_cls', ['gt_instance_ids'].
            
        Returns:
            dict: Dictionary of losses where all values are torch.Tensor objects.
        """
        # Handle predictions based on whether we're using embeddings
        if self.use_embedding:
            heatmap_pred, offset_pred, z_pred, cls_pred, embed_pred = preds
        else:
            heatmap_pred, offset_pred, z_pred, cls_pred = preds
        
        # =============================================================================
        # CRITICAL AUTOGRAD FIX: Create dummy loss tensor attached to computation graph
        # =============================================================================
        # Instead of using `heatmap_pred.new_tensor(0.0)` which creates a detached leaf node,
        # we compute a "dummy" zero loss by multiplying model predictions by 0.0.
        # This ensures the tensor remains connected to the computation graph with valid grad_fn.
        dummy_loss = (heatmap_pred.sum() + offset_pred.sum() + z_pred.sum() + cls_pred.sum()) * 0.0
        if self.use_embedding:
            dummy_loss += embed_pred.sum() * 0.0
        
        # DEBUG: Verify autograd connectivity
        # print(f"[AUTOGRAD_DEBUG] dummy_loss requires_grad: {dummy_loss.requires_grad}")
        # print(f"[AUTOGRAD_DEBUG] dummy_loss grad_fn: {dummy_loss.grad_fn}")
        # print(f"[AUTOGRAD_DEBUG] dummy_loss device: {dummy_loss.device}")
        # print(f"[AUTOGRAD_DEBUG] dummy_loss value: {dummy_loss.item()}")
        
        # Initialize loss dictionary with graph-connected dummy tensors
        loss_dict = {
            'loss_heatmap': dummy_loss.clone(),
            'loss_offset': dummy_loss.clone(),
            'loss_z': dummy_loss.clone(),
            'loss_cls': dummy_loss.clone()
        }
        if self.use_embedding:
            loss_dict['loss_embedding'] = dummy_loss.clone()
        
        # =============================================================================
        # EDGE CASE HANDLING: All branches return graph-connected tensors
        # =============================================================================
        
        # Early return with dummy loss if no targets available
        if lane_targets is None:
            print("[LANE_HEAD_LOSS] lane_targets is None, returning graph-connected dummy zero losses")
            # print(f"[AUTOGRAD_DEBUG] Returning dummy losses - all have grad_fn: {all(v.grad_fn is not None for v in loss_dict.values())}")
            return loss_dict

        # Transform targets
        transformed_targets = self._transform_lane_targets(lane_targets)
        if transformed_targets is None:
            print("[LANE_HEAD_LOSS] transformed_targets is None, returning graph-connected dummy zero losses")
            # print(f"[AUTOGRAD_DEBUG] Returning dummy losses - all have grad_fn: {all(v.grad_fn is not None for v in loss_dict.values())}")
            return loss_dict

        # Extract ground truth data
        gt_heatmap = transformed_targets['gt_heatmap']
        gt_offset = transformed_targets['gt_offset']
        gt_height = transformed_targets['gt_height']
        gt_mask = transformed_targets['gt_mask']
        gt_cls = transformed_targets['gt_cls']
        
        # =============================================================================
        # DEFENSIVE: Ensure all GT tensors have batch dimension
        # =============================================================================
        def ensure_batch_dim(tensor, name, expected_batch_size=1):
            """Ensure tensor has batch dimension. Add if missing."""
            if isinstance(tensor, torch.Tensor):
                if name in ['gt_heatmap', 'gt_offset', 'gt_height', 'gt_mask'] and tensor.dim() == 3:
                    # These should be [B, C=1, H, W], but we got [C=1, H, W]
                    print(f"[AUTOGRAD_DEFENSIVE] Warning: GT tensor '{name}' missing batch dim. Adding it.")
                    return tensor.unsqueeze(0)
                elif name == 'gt_cls' and tensor.dim() == 3:
                    # gt_cls should be [B, C, H, W], but we got [C, H, W]
                    print(f"[AUTOGRAD_DEFENSIVE] Warning: GT tensor '{name}' missing batch dim. Adding it.")
                    return tensor.unsqueeze(0)
                elif name == 'gt_instance_ids' and tensor.dim() == 2:
                    # instance_ids should be [B, H, W], but we got [H, W]
                    print(f"[AUTOGRAD_DEFENSIVE] Warning: GT tensor '{name}' missing batch dim. Adding it.")
                    return tensor.unsqueeze(0)
            return tensor
        
        gt_heatmap = ensure_batch_dim(gt_heatmap, 'gt_heatmap')
        gt_offset = ensure_batch_dim(gt_offset, 'gt_offset') 
        gt_height = ensure_batch_dim(gt_height, 'gt_height')
        gt_mask = ensure_batch_dim(gt_mask, 'gt_mask')
        gt_cls = ensure_batch_dim(gt_cls, 'gt_cls')
        
        # Check if we have any valid targets (non-zero mask)
        has_valid_targets = gt_mask.sum() > 0
        
        # DEBUG: Check target shapes and device placement
        # print(f"[LANE_HEAD_LOSS] Target shapes: heatmap={gt_heatmap.shape}, mask={gt_mask.shape}, offset={gt_offset.shape}")
        # print(f"[LANE_HEAD_LOSS] Pred shapes: heatmap={heatmap_pred.shape}, offset={offset_pred.shape}, cls={cls_pred.shape}")
        # print(f"[LANE_HEAD_LOSS] has_valid_targets: {has_valid_targets} (mask_sum={gt_mask.sum().item()})")
        
        # =============================================================================
        # VALID TARGET PROCESSING: Compute actual losses
        # =============================================================================
        
        if has_valid_targets:
            # print("[LANE_HEAD_LOSS] Computing actual losses for valid targets")
            
            # Squeeze predictions to match ground truth dimensions
            heatmap_pred_squeezed = heatmap_pred.squeeze(1)  # [B, 1, H, W] -> [B, H, W]
            offset_pred_squeezed = offset_pred.squeeze(1)    # [B, 1, H, W] -> [B, H, W]
            z_pred_squeezed = z_pred.squeeze(1)              # [B, 1, H, W] -> [B, H, W]
            
            # Squeeze ground truth to match prediction dimensions
            gt_heatmap_squeezed = gt_heatmap.squeeze(1)      # [B, 1, H, W] -> [B, H, W]
            gt_offset_squeezed = gt_offset.squeeze(1)        # [B, 1, H, W] -> [B, H, W]
            gt_height_squeezed = gt_height.squeeze(1)        # [B, 1, H, W] -> [B, H, W]
            gt_mask_squeezed = gt_mask.squeeze(1)            # [B, 1, H, W] -> [B, H, W]
            
            # # DEBUG: Check tensor shapes before loss computation
            # print(f"[SHAPE_DEBUG] After squeezing:")
            # print(f"[SHAPE_DEBUG]   heatmap_pred_squeezed: {heatmap_pred_squeezed.shape}")
            # print(f"[SHAPE_DEBUG]   gt_heatmap_squeezed: {gt_heatmap_squeezed.shape}")
            # print(f"[SHAPE_DEBUG]   offset_pred_squeezed: {offset_pred_squeezed.shape}")
            # print(f"[SHAPE_DEBUG]   gt_offset_squeezed: {gt_offset_squeezed.shape}")
            # print(f"[SHAPE_DEBUG]   gt_mask_squeezed: {gt_mask_squeezed.shape}")
            # print(f"[SHAPE_DEBUG]   cls_pred: {cls_pred.shape}")
            # print(f"[SHAPE_DEBUG]   gt_cls: {gt_cls.shape}")
            
            # Compute individual losses
            loss_heatmap = self.loss_heatmap(heatmap_pred_squeezed, gt_heatmap_squeezed)
            loss_offset = self.loss_offset(offset_pred_squeezed, gt_offset_squeezed, weight=gt_mask_squeezed)
            loss_z = self.loss_z(z_pred_squeezed, gt_height_squeezed, weight=gt_mask_squeezed)
            
            # DEBUG: Verify computed losses have gradients
            # print(f"[AUTOGRAD_DEBUG] loss_heatmap requires_grad: {loss_heatmap.requires_grad}, grad_fn: {loss_heatmap.grad_fn}")
            # print(f"[AUTOGRAD_DEBUG] loss_offset requires_grad: {loss_offset.requires_grad}, grad_fn: {loss_offset.grad_fn}")
            # print(f"[AUTOGRAD_DEBUG] loss_z requires_grad: {loss_z.requires_grad}, grad_fn: {loss_z.grad_fn}")
            
            # Classification loss with proper edge case handling
            B, num_classes_pred, H, W = cls_pred.shape
            valid_mask = gt_mask_squeezed > 0.5  # Shape: [B, H, W]
            
            # 保留关键的分类损失信息
            # print(f"[CLS_LOSS_DEBUG] cls_pred shape: {cls_pred.shape}, gt_cls shape: {gt_cls.shape}")
            # print(f"[CLS_LOSS_DEBUG] valid_mask sum: {valid_mask.sum().item()}, gt_cls non-zero: {(gt_cls > 0).sum().item()}")
            
            # print(f"[CLS_LOSS_DEBUG] === CLASSIFICATION LOSS COMPUTATION ===")
            # print(f"[CLS_LOSS_DEBUG] valid_mask shape: {valid_mask.shape}")
            # 
            # # 检查gt_cls中的非零元素
            # gt_cls_nonzero = (gt_cls > 0).sum().item()
            # print(f"[CLS_LOSS_DEBUG] gt_cls non-zero elements: {gt_cls_nonzero}")
            # 
            # # 统计每个类别的GT像素数
            # for c in range(gt_cls.shape[1]):
            #     class_pixels = (gt_cls[:, c, :, :] > 0).sum().item()
            #     if class_pixels > 0:
            #         print(f"[CLS_LOSS_DEBUG] GT class {c}: {class_pixels} pixels")
            
            # Only compute classification loss if there are valid pixels
            if valid_mask.sum() > 0:
                # Reshape for filtering
                cls_pred_permuted = cls_pred.permute(0, 2, 3, 1).contiguous() # Shape: [B, H, W, C]
                gt_cls_permuted = gt_cls.permute(0, 2, 3, 1).contiguous()     # Shape: [B, H, W, C]
                
                # Filter to only valid pixels
                valid_cls_pred = cls_pred_permuted[valid_mask] # Shape: [num_valid_pixels, C]
                valid_gt_cls = gt_cls_permuted[valid_mask]     # Shape: [num_valid_pixels, C]
                
                # Convert one-hot GT to class indices
                valid_gt_indices = torch.argmax(valid_gt_cls, dim=1)
                
                # Calculate classification loss
                loss_cls = self.loss_cls(valid_cls_pred, valid_gt_indices)
                # print(f"[CLS_LOSS_DEBUG] loss_cls value: {loss_cls.item():.6f}")
                
                # print(f"[CLS_LOSS_DEBUG] After permute:")
                # print(f"[CLS_LOSS_DEBUG]   cls_pred_permuted: {cls_pred_permuted.shape}")
                # print(f"[CLS_LOSS_DEBUG]   gt_cls_permuted: {gt_cls_permuted.shape}")
                # 
                # print(f"[CLS_LOSS_DEBUG] After filtering:")
                # print(f"[CLS_LOSS_DEBUG]   valid_cls_pred: {valid_cls_pred.shape}")
                # print(f"[CLS_LOSS_DEBUG]   valid_gt_cls: {valid_gt_cls.shape}")
                # 
                # print(f"[CLS_LOSS_DEBUG] valid_gt_indices shape: {valid_gt_indices.shape}")
                # print(f"[CLS_LOSS_DEBUG] valid_gt_indices unique values: {torch.unique(valid_gt_indices).tolist()}")
                # print(f"[CLS_LOSS_DEBUG] loss_cls requires_grad: {loss_cls.requires_grad}, grad_fn: {loss_cls.grad_fn}")
            else:
                # No valid pixels for classification - keep dummy loss
                # print(f"[CLS_LOSS_DEBUG] No valid pixels for classification loss, using dummy loss")
                loss_cls = dummy_loss.clone()
            
            # Embedding loss computation
            if self.use_embedding and 'gt_instance_ids' in transformed_targets:
                gt_instance_ids = transformed_targets['gt_instance_ids']
                gt_instance_ids = ensure_batch_dim(gt_instance_ids, 'gt_instance_ids')
                
                # 保留关键的嵌入损失设置信息
                unique_gt_instances = torch.unique(gt_instance_ids)
                # The mask from the pipeline is (B, 1, H, W). Use already squeezed mask.
                embedding_valid_mask = gt_mask_squeezed
                
                # Additional safety check: ensure mask is on correct device
                if embedding_valid_mask.device != embed_pred.device:
                    embedding_valid_mask = embedding_valid_mask.to(embed_pred.device)
                
                if gt_instance_ids.device != embed_pred.device:
                    gt_instance_ids = gt_instance_ids.to(embed_pred.device)
                
                # Only compute embedding loss if there are valid targets
                if embedding_valid_mask.sum() > 0:
                    embedding_losses = self.loss_embedding(
                        embed_pred, gt_instance_ids, valid_mask=embedding_valid_mask
                    )
                    
                    if isinstance(embedding_losses, dict):
                        # Extract the main loss (this will be summed in the total loss)
                        loss_embedding = embedding_losses['loss']
                    else:
                        loss_embedding = embedding_losses
                    # print(f"[AUTOGRAD_DEBUG] loss_embedding value: {loss_embedding.item():.6f}")
                else:
                    # print("[LANE_HEAD_LOSS] No valid targets for embedding loss, using dummy loss")
                    loss_embedding = dummy_loss.clone()
            else:
                # print("[LANE_HEAD_LOSS] No embedding configuration or gt_instance_ids, using dummy loss")
                loss_embedding = dummy_loss.clone()
            
            # Update loss dictionary with computed values
            loss_dict.update({
                'loss_heatmap': loss_heatmap,
                'loss_offset': loss_offset,
                'loss_z': loss_z,
                'loss_cls': loss_cls
            })
            
            if self.use_embedding:
                loss_dict['loss_embedding'] = loss_embedding
                
        # 保留关键的损失信息
        total_loss_sum = 0.0
        for key, value in loss_dict.items():
            loss_value = value.item()
            total_loss_sum += loss_value
            # print(f"[LOSS_DEBUG {key}: {loss_value:.6f}")
        
        # print(f"[LOSS_DEBUG] Total loss sum: {total_loss_sum:.6f}")
        
        return loss_dict
    
    def _nms(self, heat):
        """Non-maximum suppression for heatmap.
        
        Args:
            heat (torch.Tensor): Input heatmap.
            
        Returns:
            torch.Tensor: Heatmap after NMS.
        """
        # Get kernel for max pooling
        kernel = self.nms_kernel_size
        pad = (kernel - 1) // 2
        
        # Apply max pooling to get local maximum
        hmax = F.max_pool2d(
            heat, kernel_size=kernel, stride=1, padding=pad)
        # Keep only the exact local maximum
        keep = (hmax == heat).float()
        
        return heat * keep
    
    def _topk(self, heatmap, k=20):
        """Extract top-k points from heatmap.
        
        Args:
            heatmap (torch.Tensor): Input heatmap.
            k (int): Number of top points to extract.
            
        Returns:
            tuple: A tuple containing topk scores, indices, classes, ys, xs.
        """
        batch, cat, height, width = heatmap.size()
        
        # Reshape and find top k scores
        heatmap = heatmap.view(batch, -1)
        topk_scores, topk_inds = torch.topk(heatmap, k)
        
        # Get coordinates from indices
        topk_ys = (topk_inds // width).float()
        topk_xs = (topk_inds % width).float()
        
        # Note: topk_classes is placeholder to maintain API compatibility
        # It's not actually used in our implementation
        topk_classes = torch.zeros_like(topk_ys)
        
        return topk_scores, topk_inds, topk_classes, topk_ys, topk_xs

    
    def _group_lane_points(self, points, class_ids, scores, max_dist=1.0):
        """Group lane points into continuous lane instances using geometric approach.
        
        Args:
            points (torch.Tensor): 3D points (N, 3).
            class_ids (torch.Tensor): Class IDs for each point (N,).
            scores (torch.Tensor): Detection scores for each point (N,).
            max_dist (float): Maximum distance to consider points part of the same lane.
            
        Returns:
            list: List of grouped lane instances.
        """
        if len(points) == 0:
            return []
            
        # Sort points by y-coordinate
        sorted_indices = torch.argsort(points[:, 1])
        sorted_points = points[sorted_indices]
        sorted_class_ids = class_ids[sorted_indices]
        sorted_scores = scores[sorted_indices]
        
        # Group by both distance and angle consistency
        lanes = []
        current_lane_indices = [0]
        current_lane_class = sorted_class_ids[0].item()
        current_direction = None
        
        for i in range(1, len(sorted_points)):
            curr_point = sorted_points[i]
            curr_class = sorted_class_ids[i].item()
            
            # Get last point in current lane
            last_idx = current_lane_indices[-1]
            last_point = sorted_points[last_idx]
            
            # Calculate spatial distance
            dist = torch.norm(curr_point[:2] - last_point[:2])
            
            # Calculate direction if we have enough points
            if len(current_lane_indices) >= 2:
                prev_point = sorted_points[current_lane_indices[-2]]
                new_direction = (last_point[:2] - prev_point[:2])
                new_direction = new_direction / (torch.norm(new_direction) + 1e-6)
                
                if current_direction is None:
                    current_direction = new_direction
                
                # Check direction consistency
                direction_sim = torch.dot(current_direction, new_direction)
                direction_consistent = direction_sim > 0.707  # cos(45°)
            else:
                direction_consistent = True
            
            # Determine if point belongs to current lane
            if (dist < max_dist and curr_class == current_lane_class and direction_consistent):
                current_lane_indices.append(i)
                
                # Update direction as moving average
                if len(current_lane_indices) >= 2 and current_direction is not None:
                    prev_point = sorted_points[current_lane_indices[-2]]
                    new_direction = (curr_point[:2] - prev_point[:2])
                    new_direction = new_direction / (torch.norm(new_direction) + 1e-6)
                    current_direction = 0.7 * current_direction + 0.3 * new_direction
                    current_direction = current_direction / (torch.norm(current_direction) + 1e-6)
            else:
                # Create new lane if current has enough points
                if len(current_lane_indices) >= 3:
                    lane_points = sorted_points[current_lane_indices]
                    lane_scores = sorted_scores[current_lane_indices]
                    
                    lane = {
                        'points_3d': lane_points,
                        'class_id': current_lane_class,
                        'score': lane_scores.mean().item()
                    }
                    lanes.append(lane)
                
                # Start new lane
                current_lane_indices = [i]
                current_lane_class = curr_class
                current_direction = None
        
        # Add the last lane
        if len(current_lane_indices) >= 3:
            lane_points = sorted_points[current_lane_indices]
            lane_scores = sorted_scores[current_lane_indices]
            
            lane = {
                'points_3d': lane_points,
                'class_id': current_lane_class,
                'score': lane_scores.mean().item()
            }
            lanes.append(lane)
        
        return lanes
    
    def _debug_visualize_clustering(self, points, embeddings, class_ids, scores, cluster_labels, cls_id, save_dir="debug_clustering"):
        """Debug visualization to show clustering results."""
        if not hasattr(self, '_debug_counter'):
            self._debug_counter = 0
        self._debug_counter += 1
        
        # Create debug directory
        os.makedirs(save_dir, exist_ok=True)
        
        # Convert to numpy for plotting
        points_np = points.detach().cpu().numpy()
        embeddings_np = embeddings.detach().cpu().numpy()
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Plot 1: Spatial distribution colored by cluster
        unique_clusters = np.unique(cluster_labels)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_clusters)))
        
        for i, cluster_id in enumerate(unique_clusters):
            mask = cluster_labels == cluster_id
            if cluster_id == -1:  # Noise points
                ax1.scatter(points_np[mask, 0], points_np[mask, 1], 
                           c='black', marker='x', s=50, alpha=0.7, label='Noise')
            else:
                ax1.scatter(points_np[mask, 0], points_np[mask, 1], 
                           c=[colors[i]], s=60, alpha=0.8, label=f'Cluster {cluster_id}')
        
        ax1.set_xlabel('X (meters)')
        ax1.set_ylabel('Y (meters)')
        ax1.set_title(f'Spatial Clustering Results (Class {cls_id})\neps={self.clustering_epsilon}, min_samples={self.clustering_min_points}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Embedding space (first 2 dimensions)
        if embeddings_np.shape[1] >= 2:
            for i, cluster_id in enumerate(unique_clusters):
                mask = cluster_labels == cluster_id
                if cluster_id == -1:  # Noise points
                    ax2.scatter(embeddings_np[mask, 0], embeddings_np[mask, 1], 
                               c='black', marker='x', s=50, alpha=0.7, label='Noise')
                else:
                    ax2.scatter(embeddings_np[mask, 0], embeddings_np[mask, 1], 
                               c=[colors[i]], s=60, alpha=0.8, label=f'Cluster {cluster_id}')
            
            ax2.set_xlabel('Embedding Dim 0')
            ax2.set_ylabel('Embedding Dim 1')
            ax2.set_title(f'Embedding Space Clustering (Class {cls_id})')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # Add statistics text
        stats_text = f"Total points: {len(points_np)}\n"
        stats_text += f"Clusters found: {len(unique_clusters) - (1 if -1 in unique_clusters else 0)}\n"
        stats_text += f"Noise points: {np.sum(cluster_labels == -1)}\n"
        stats_text += f"Embedding variance: {np.var(embeddings_np, axis=0).mean():.4f}"
        
        fig.text(0.02, 0.98, stats_text, fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        save_path = os.path.join(save_dir, f'clustering_debug_{self._debug_counter:04d}_class_{cls_id}.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"[CLUSTERING_DEBUG] Saved visualization: {save_path}")
        print(f"[CLUSTERING_DEBUG] Class {cls_id}: {len(unique_clusters) - (1 if -1 in unique_clusters else 0)} clusters, {np.sum(cluster_labels == -1)} noise points")
    
    def _group_lane_points_embedding(self, points, embeddings, class_ids, scores):
        """Improved embedding-based clustering with debug visualization"""
        if len(points) == 0:
            return []
        
        # Normalize embeddings for better clustering performance
        embeddings_norm = F.normalize(embeddings, p=2, dim=1)
        
        # Convert to numpy for DBSCAN
        embeddings_np = embeddings_norm.detach().cpu().numpy()
        
        # Group by class first, then cluster embeddings
        lanes = []
        unique_classes = torch.unique(class_ids)
        
        for cls_id in unique_classes:
            # Extract points for this class
            cls_mask = class_ids == cls_id
            if not cls_mask.sum() > self.clustering_min_points:
                continue
            
            cls_points = points[cls_mask]
            cls_embeddings = embeddings_np[cls_mask.cpu().numpy()]
            cls_scores = scores[cls_mask]
            
            # Apply DBSCAN clustering
            clustering = DBSCAN(
                eps=self.clustering_epsilon,
                min_samples=self.clustering_min_points,
                metric='euclidean'
            ).fit(cls_embeddings)
            
            # DEBUG: Visualize clustering results
            cluster_labels = clustering.labels_
            if hasattr(self, 'debug_clustering') and self.debug_clustering:
                self._debug_visualize_clustering(
                    cls_points, embeddings_norm[cls_mask], class_ids[cls_mask], 
                    cls_scores, cluster_labels, cls_id.item()
                )
            
            # Process clusters
            for cluster_id in np.unique(cluster_labels):
                if cluster_id == -1:  # Skip noise
                    continue
                
                # Get points for this cluster
                cluster_mask = cluster_labels == cluster_id
                lane_points = cls_points[cluster_mask]
                lane_scores = cls_scores[cluster_mask]
                
                if len(lane_points) < 3:  # Skip too small clusters
                    continue
                
                # Sort by y coordinate
                sort_idx = torch.argsort(lane_points[:, 1])
                lane_points = lane_points[sort_idx]
                
                lanes.append({
                    'points_3d': lane_points,
                    'class_id': cls_id.item(),
                    'score': lane_scores.mean().item()
                })
        
        return lanes
    
    def get_lanes(self, preds, img_metas, rescale=False):
        """Post-process predictions to get lane instances.
        
        Args:
            preds (tuple): Prediction tuple (heatmap, offset, z, cls, [embed]).
            img_metas (list): List of image meta information.
            rescale (bool): Whether to rescale output.
            
        Returns:
            list: List of detected lanes with 3D coordinates and class labels.
        """
        # Handle predictions based on whether we're using embeddings
        if self.use_embedding:
            heatmap, offset, z_pred, cls_pred, embed_pred = preds
        else:
            heatmap, offset, z_pred, cls_pred = preds
            
        batch_size = heatmap.size(0)
        
        # Apply NMS to heatmap
        heatmap = self._nms(heatmap)
        
        # Extract top k lane points from each sample
        # Note: _topk returns (scores, inds, classes, ys, xs) where:
        # - ys corresponds to height dimension (Y-axis in world coordinates)
        # - xs corresponds to width dimension (X-axis in world coordinates)
        # scores, inds, _, xs_grid, ys_grid = self._topk(heatmap, k=self.max_lanes)
        scores, inds, _, ys_grid, xs_grid = self._topk(heatmap, k=self.max_lanes)
        
        # Only keep points with score above threshold
        keep = scores > self.hm_thres
        
        lane_instances = []
        for b in range(batch_size):
            # Lanes for this sample
            sample_lanes = []
            
            mask = keep[b]
            if not mask.any():
                # No lanes detected for this sample
                lane_instances.append(sample_lanes)
                continue
                
            # Get coordinates for valid detections
            # These are grid indices (floating point)
            xs_b_grid = xs_grid[b][mask]  # Width dimension indices (X-axis)
            ys_b_grid = ys_grid[b][mask]  # Height dimension indices (Y-axis)
            scores_b = scores[b][mask]
            
            # Convert grid indices to world coordinates
            # CRITICAL: Correct mapping based on tensor dimensions:
            # - xs_grid (width dim) -> X-axis world coordinates
            # - ys_grid (height dim) -> Y-axis world coordinates
            x_coords_world = xs_b_grid * self.x_res + self.x_min
            y_coords_world = ys_b_grid * self.y_res + self.y_min
            
            # Get corresponding offsets, z-heights, and class predictions
            inds_b = inds[b][mask]
            
            # Convert to integer grid indices for feature extraction
            # CRITICAL: Tensor format is [batch, channel, height, width]
            # - ys_grid corresponds to height dimension (dim 2)
            # - xs_grid corresponds to width dimension (dim 3)
            ys_b_int = ys_b_grid.long()  # Height indices for tensor dim 2
            xs_b_int = xs_b_grid.long()  # Width indices for tensor dim 3
            
            # Ensure indices are within bounds
            ys_b_int = torch.clamp(ys_b_int, 0, heatmap.shape[2] - 1)  # Height bound check
            xs_b_int = torch.clamp(xs_b_int, 0, heatmap.shape[3] - 1)  # Width bound check
            
            # Extract features at grid locations
            # CRITICAL: Use correct tensor indexing [batch, channel, height_idx, width_idx]
            offsets_b = offset[b, 0, ys_b_int, xs_b_int]
            z_vals_b = z_pred[b, 0, ys_b_int, xs_b_int]
            
            # CRITICAL FIX: Apply Y-direction offset instead of X-direction
            # According to BEV-LaneDet methodology, offset represents lateral deviation (Y-direction)
            y_coords_world_adjusted = y_coords_world + offsets_b * self.y_res
            # Keep X coordinates unchanged as they are already accurate from grid positioning
            x_coords_world_final = x_coords_world
            
            # Get class predictions
            # CRITICAL: Use correct tensor indexing [batch, channel, height_idx, width_idx]
            cls_scores = cls_pred[b, :, ys_b_int, xs_b_int]  # [num_classes, num_points]
            cls_scores = cls_scores.transpose(0, 1)  # [num_points, num_classes]
            cls_ids = torch.argmax(cls_scores, dim=1)  # [num_points]
            
            # Collect all points in world coordinates
            # Use adjusted Y coordinates and final X coordinates
            points_3d = torch.stack([x_coords_world_final, y_coords_world_adjusted, z_vals_b], dim=1)
            
            # Group points based on the selected method
            if self.use_embedding and self.clustering_method == 'dbscan':
                # Extract embeddings at grid locations
                # CRITICAL: Use correct tensor indexing [batch, channel, height_idx, width_idx]
                embeddings_b = embed_pred[b, :, ys_b_int, xs_b_int].transpose(0, 1)  # [num_points, embed_dim]
                
                # Group points using embedding-based clustering
                grouped_lanes = self._group_lane_points_embedding(
                    points_3d, embeddings_b, cls_ids, scores_b)
                sample_lanes.extend(grouped_lanes)
            elif self.group_lanes:
                # Group points using the geometric approach
                grouped_lanes = self._group_lane_points(
                    points_3d, cls_ids, scores_b, max_dist=self.lane_group_min_distance)
                sample_lanes.extend(grouped_lanes)
            else:
                # Organize individual points into lane instances
                for i in range(len(x_coords_world_final)):
                    lane = {
                        'points_3d': points_3d[i].unsqueeze(0),  # (1, 3)
                        'class_id': cls_ids[i].item(),
                        'score': scores_b[i].item()
                    }
                    sample_lanes.append(lane)
            
            lane_instances.append(sample_lanes)
            
        return lane_instances